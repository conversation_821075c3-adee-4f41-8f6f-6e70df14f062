/**
 * 临时邮箱助手 - Service Worker
 * 负责后台任务管理、API代理和消息传递
 */

// 配置常量
const CONFIG = {
  API_BASE_URL: 'http://1.12.224.176:8080/api',
  API_KEY: 'mailplugin_api_key_2024',
  POLL_INTERVAL: 5000, // 5秒轮询间隔
  MAX_RETRIES: 3
};

// 全局状态管理
let activePolling = new Map(); // 存储活跃的轮询任务
let userSessions = new Map(); // 存储用户会话信息

/**
 * 插件安装时的初始化
 */
chrome.runtime.onInstalled.addListener((details) => {
  console.log('临时邮箱助手已安装', details);
  
  // 设置侧边栏
  chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
  
  // 初始化存储
  initializeStorage();
});

/**
 * 插件启动时的初始化
 */
chrome.runtime.onStartup.addListener(() => {
  console.log('临时邮箱助手已启动');
  initializeStorage();
});

/**
 * 初始化存储
 */
async function initializeStorage() {
  try {
    // 恢复用户会话
    const result = await chrome.storage.local.get(['userSessions']);
    if (result.userSessions) {
      userSessions = new Map(Object.entries(result.userSessions));
      console.log('恢复用户会话:', userSessions.size);
    }
  } catch (error) {
    console.error('初始化存储失败:', error);
  }
}

/**
 * 处理来自侧边栏的消息
 */
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('收到消息:', message);
  
  switch (message.action) {
    case 'generateEmail':
      handleGenerateEmail(message.data, sendResponse);
      break;
      
    case 'getEmails':
      handleGetEmails(message.data, sendResponse);
      break;
      
    case 'startPolling':
      handleStartPolling(message.data, sendResponse);
      break;
      
    case 'stopPolling':
      handleStopPolling(message.data, sendResponse);
      break;
      
    case 'getSharedRules':
      handleGetSharedRules(sendResponse);
      break;
      
    case 'uploadSharedRule':
      handleUploadSharedRule(message.data, sendResponse);
      break;
      
    default:
      sendResponse({ success: false, message: '未知的操作类型' });
  }
  
  return true; // 保持消息通道开放
});

/**
 * 处理生成临时邮箱请求
 */
async function handleGenerateEmail(data, sendResponse) {
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/generate-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: data.userId,
        tempEmail: data.tempEmail,
        generateTime: data.generateTime,
        apiKey: CONFIG.API_KEY
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // 保存用户会话信息
      userSessions.set(data.userId, {
        tempEmail: data.tempEmail,
        generateTime: data.generateTime,
        lastCheck: new Date().toISOString(),
        lastNotifiedCount: 0 // 添加邮件通知计数器
      });

      // 保存到本地存储
      await saveUserSession(data.userId, userSessions.get(data.userId));
    }
    
    sendResponse(result);
  } catch (error) {
    console.error('生成邮箱请求失败:', error);
    sendResponse({ 
      success: false, 
      message: '网络请求失败: ' + error.message 
    });
  }
}

/**
 * 处理获取邮件请求
 */
async function handleGetEmails(data, sendResponse) {
  try {
    const url = `${CONFIG.API_BASE_URL}/get-emails?userId=${encodeURIComponent(data.userId)}&apiKey=${encodeURIComponent(CONFIG.API_KEY)}`;
    const response = await fetch(url);
    const result = await response.json();
    
    sendResponse(result);
  } catch (error) {
    console.error('获取邮件请求失败:', error);
    sendResponse({ 
      success: false, 
      message: '网络请求失败: ' + error.message 
    });
  }
}

/**
 * 处理开始轮询请求
 */
async function handleStartPolling(data, sendResponse) {
  const userId = data.userId;
  
  if (activePolling.has(userId)) {
    sendResponse({ success: false, message: '轮询已在进行中' });
    return;
  }
  
  const intervalId = setInterval(async () => {
    try {
      const url = `${CONFIG.API_BASE_URL}/get-emails?userId=${encodeURIComponent(userId)}&apiKey=${encodeURIComponent(CONFIG.API_KEY)}`;
      const response = await fetch(url);
      const result = await response.json();
      
      if (result.success && result.emails && result.emails.length > 0) {
        // 检查是否有新邮件
        const session = userSessions.get(userId);
        if (session && result.emails.length > session.lastNotifiedCount) {
          // 发送通知给侧边栏
          chrome.runtime.sendMessage({
            action: 'emailsUpdated',
            data: result
          });
          
          // 更新通知计数
          session.lastNotifiedCount = result.emails.length;
          userSessions.set(userId, session);
          await saveUserSession(userId, session);
        }
      }
    } catch (error) {
      console.error('轮询获取邮件失败:', error);
    }
  }, CONFIG.POLL_INTERVAL);
  
  activePolling.set(userId, intervalId);
  sendResponse({ success: true, message: '轮询已开始' });
}

/**
 * 处理停止轮询请求
 */
async function handleStopPolling(data, sendResponse) {
  const userId = data.userId;
  const intervalId = activePolling.get(userId);
  
  if (intervalId) {
    clearInterval(intervalId);
    activePolling.delete(userId);
    sendResponse({ success: true, message: '轮询已停止' });
  } else {
    sendResponse({ success: false, message: '没有活跃的轮询' });
  }
}

/**
 * 处理获取共享规则请求
 */
async function handleGetSharedRules(sendResponse) {
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/get-shared-rules`);
    const result = await response.json();
    sendResponse(result);
  } catch (error) {
    console.error('获取共享规则失败:', error);
    sendResponse({ 
      success: false, 
      message: '网络请求失败: ' + error.message 
    });
  }
}

/**
 * 处理上传共享规则请求
 */
async function handleUploadSharedRule(data, sendResponse) {
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/upload-shared-rule`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        rule: data.rule,
        apiKey: CONFIG.API_KEY
      })
    });
    
    const result = await response.json();
    sendResponse(result);
  } catch (error) {
    console.error('上传共享规则失败:', error);
    sendResponse({ 
      success: false, 
      message: '网络请求失败: ' + error.message 
    });
  }
}

/**
 * 保存用户会话到本地存储
 */
async function saveUserSession(userId, session) {
  try {
    const sessionsObj = Object.fromEntries(userSessions);
    await chrome.storage.local.set({ userSessions: sessionsObj });
  } catch (error) {
    console.error('保存用户会话失败:', error);
  }
}

/**
 * 清理过期会话
 */
function cleanExpiredSessions() {
  const now = Date.now();
  const expireTime = 24 * 60 * 60 * 1000; // 24小时
  
  for (const [userId, session] of userSessions.entries()) {
    const sessionTime = new Date(session.generateTime).getTime();
    if (now - sessionTime > expireTime) {
      userSessions.delete(userId);
      // 停止相关轮询
      const intervalId = activePolling.get(userId);
      if (intervalId) {
        clearInterval(intervalId);
        activePolling.delete(userId);
      }
    }
  }
}

// 定期清理过期会话
setInterval(cleanExpiredSessions, 60 * 60 * 1000); // 每小时清理一次
