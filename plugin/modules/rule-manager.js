/**
 * 解析规则管理模块
 * 负责创建、管理和应用邮件解析规则
 */

import { storageManager } from './storage-manager.js';
import { apiClient } from './api-client.js';

/**
 * 解析规则管理器类
 */
export class RuleManager {
  constructor() {
    this.localRules = [];
    this.sharedRules = [];
    this.ruleCache = new Map();
  }

  /**
   * 初始化规则管理器
   */
  async initialize() {
    try {
      await this.loadLocalRules();
      await this.loadSharedRules();
      console.log('规则管理器初始化完成');
    } catch (error) {
      console.error('规则管理器初始化失败:', error);
    }
  }

  /**
   * 加载本地规则
   */
  async loadLocalRules() {
    try {
      this.localRules = await storageManager.loadLocalRules();
      console.log(`加载本地规则: ${this.localRules.length} 条`);
      this.updateRuleCache();
    } catch (error) {
      console.error('加载本地规则失败:', error);
      this.localRules = [];
    }
  }

  /**
   * 加载共享规则
   */
  async loadSharedRules() {
    try {
      const response = await apiClient.getSharedRules();
      if (response.success && response.rules) {
        this.sharedRules = response.rules.map(rule => ({
          ...rule,
          isShared: true,
          isActive: true
        }));
        console.log(`加载共享规则: ${this.sharedRules.length} 条`);
        this.updateRuleCache();
      } else {
        console.warn('获取共享规则失败:', response.message);
        this.sharedRules = [];
      }
    } catch (error) {
      console.error('加载共享规则失败:', error);
      this.sharedRules = [];
    }
  }

  /**
   * 创建新规则
   * @param {Object} ruleData 规则数据
   * @returns {Object} 创建的规则
   */
  createRule(ruleData) {
    const rule = {
      id: this.generateRuleId(),
      senderEmail: ruleData.senderEmail?.toLowerCase().trim(),
      platform: ruleData.platform?.trim(),
      pattern: ruleData.pattern?.trim(),
      patternType: ruleData.patternType,
      example: ruleData.example?.trim(),
      description: ruleData.description?.trim() || `${ruleData.platform}解析规则`,
      createdTime: new Date().toISOString(),
      isActive: true,
      isLocal: true,
      isShared: false
    };

    // 验证规则
    if (!this.validateRule(rule)) {
      throw new Error('规则验证失败');
    }

    return rule;
  }

  /**
   * 保存本地规则
   * @param {Object} rule 规则对象
   * @returns {Promise<boolean>} 是否保存成功
   */
  async saveLocalRule(rule) {
    try {
      // 检查是否已存在相同的规则
      const existingRule = this.localRules.find(r => 
        r.senderEmail === rule.senderEmail && r.patternType === rule.patternType
      );

      if (existingRule) {
        // 更新现有规则
        Object.assign(existingRule, rule, {
          updatedTime: new Date().toISOString()
        });
      } else {
        // 添加新规则
        this.localRules.push(rule);
      }

      const success = await storageManager.saveLocalRules(this.localRules);
      if (success) {
        this.updateRuleCache();
        console.log('本地规则保存成功:', rule.id);
      }
      return success;
    } catch (error) {
      console.error('保存本地规则失败:', error);
      return false;
    }
  }

  /**
   * 上传共享规则
   * @param {Object} rule 规则对象
   * @returns {Promise<Object>} 上传结果
   */
  async uploadSharedRule(rule) {
    try {
      const sharedRule = {
        ...rule,
        isShared: true,
        isLocal: false
      };

      const response = await apiClient.uploadSharedRule(sharedRule);
      
      if (response.success) {
        // 添加到本地共享规则列表
        if (response.ruleId) {
          sharedRule.id = response.ruleId;
        }
        this.sharedRules.push(sharedRule);
        this.updateRuleCache();
        console.log('共享规则上传成功:', sharedRule.id);
      }

      return response;
    } catch (error) {
      console.error('上传共享规则失败:', error);
      return { success: false, message: '上传失败: ' + error.message };
    }
  }

  /**
   * 删除本地规则
   * @param {string} ruleId 规则ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteLocalRule(ruleId) {
    try {
      const index = this.localRules.findIndex(rule => rule.id === ruleId);
      if (index === -1) {
        return false;
      }

      this.localRules.splice(index, 1);
      const success = await storageManager.saveLocalRules(this.localRules);
      
      if (success) {
        this.updateRuleCache();
        console.log('本地规则删除成功:', ruleId);
      }
      
      return success;
    } catch (error) {
      console.error('删除本地规则失败:', error);
      return false;
    }
  }

  /**
   * 切换规则状态
   * @param {string} ruleId 规则ID
   * @param {boolean} isActive 是否激活
   * @returns {Promise<boolean>} 是否更新成功
   */
  async toggleRuleStatus(ruleId, isActive) {
    try {
      const rule = this.localRules.find(r => r.id === ruleId);
      if (!rule) {
        return false;
      }

      rule.isActive = isActive;
      rule.updatedTime = new Date().toISOString();

      const success = await storageManager.saveLocalRules(this.localRules);
      if (success) {
        this.updateRuleCache();
        console.log(`规则状态更新: ${ruleId} -> ${isActive}`);
      }
      
      return success;
    } catch (error) {
      console.error('切换规则状态失败:', error);
      return false;
    }
  }

  /**
   * 根据发件人查找规则
   * @param {string} senderEmail 发件人邮箱
   * @returns {Object|null} 匹配的规则
   */
  findRuleForSender(senderEmail) {
    if (!senderEmail) return null;
    
    const normalizedSender = senderEmail.toLowerCase().trim();
    
    // 先查找缓存
    if (this.ruleCache.has(normalizedSender)) {
      return this.ruleCache.get(normalizedSender);
    }
    
    // 优先使用本地规则
    let rule = this.localRules.find(r => r.senderEmail === normalizedSender && r.isActive);
    
    // 如果本地没有，使用共享规则
    if (!rule) {
      rule = this.sharedRules.find(r => r.senderEmail === normalizedSender && r.isActive);
    }
    
    // 缓存结果
    if (rule) {
      this.ruleCache.set(normalizedSender, rule);
    }
    
    return rule || null;
  }

  /**
   * 获取所有可用规则
   * @returns {Array} 规则数组
   */
  getAllRules() {
    return [...this.localRules, ...this.sharedRules];
  }

  /**
   * 获取本地规则
   * @returns {Array} 本地规则数组
   */
  getLocalRules() {
    return this.localRules;
  }

  /**
   * 获取共享规则
   * @returns {Array} 共享规则数组
   */
  getSharedRules() {
    return this.sharedRules;
  }

  /**
   * 搜索规则
   * @param {string} query 搜索关键词
   * @returns {Array} 匹配的规则
   */
  searchRules(query) {
    if (!query) return this.getAllRules();
    
    const lowerQuery = query.toLowerCase();
    return this.getAllRules().filter(rule => 
      rule.platform?.toLowerCase().includes(lowerQuery) ||
      rule.senderEmail?.toLowerCase().includes(lowerQuery) ||
      rule.description?.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * 获取规则统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const activeLocal = this.localRules.filter(r => r.isActive).length;
    const activeShared = this.sharedRules.filter(r => r.isActive).length;
    
    return {
      total: this.localRules.length + this.sharedRules.length,
      local: this.localRules.length,
      shared: this.sharedRules.length,
      activeLocal,
      activeShared,
      totalActive: activeLocal + activeShared
    };
  }

  /**
   * 更新规则缓存
   */
  updateRuleCache() {
    this.ruleCache.clear();
    
    // 缓存所有规则
    [...this.localRules, ...this.sharedRules].forEach(rule => {
      if (rule.isActive) {
        this.ruleCache.set(rule.senderEmail, rule);
      }
    });
  }

  /**
   * 生成规则ID
   * @returns {string} 唯一ID
   */
  generateRuleId() {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 验证规则有效性
   * @param {Object} rule 规则对象
   * @returns {boolean} 是否有效
   */
  validateRule(rule) {
    if (!rule || typeof rule !== 'object') {
      return false;
    }
    
    const required = ['senderEmail', 'platform', 'pattern', 'patternType'];
    return required.every(field => rule[field] && typeof rule[field] === 'string');
  }

  /**
   * 测试规则
   * @param {Object} rule 规则对象
   * @param {string} testContent 测试内容
   * @returns {Object} 测试结果
   */
  testRule(rule, testContent) {
    if (!this.validateRule(rule) || !testContent) {
      return { success: false, message: '规则或测试内容无效' };
    }

    try {
      const regex = new RegExp(rule.pattern, 'gi');
      const matches = testContent.match(regex);
      
      return {
        success: true,
        matches: matches || [],
        count: matches ? matches.length : 0,
        message: matches ? `找到 ${matches.length} 个匹配项` : '未找到匹配项'
      };
    } catch (error) {
      return {
        success: false,
        message: '正则表达式错误: ' + error.message
      };
    }
  }

  /**
   * 导出规则
   * @param {boolean} includeLocal 是否包含本地规则
   * @param {boolean} includeShared 是否包含共享规则
   * @returns {Object} 导出数据
   */
  exportRules(includeLocal = true, includeShared = false) {
    const exportData = {
      exportTime: new Date().toISOString(),
      version: '1.0.0',
      rules: []
    };

    if (includeLocal) {
      exportData.rules.push(...this.localRules);
    }

    if (includeShared) {
      exportData.rules.push(...this.sharedRules);
    }

    return exportData;
  }

  /**
   * 导入规则
   * @param {Object} importData 导入数据
   * @returns {Promise<Object>} 导入结果
   */
  async importRules(importData) {
    try {
      if (!importData.rules || !Array.isArray(importData.rules)) {
        return { success: false, message: '导入数据格式错误' };
      }

      let imported = 0;
      let skipped = 0;

      for (const rule of importData.rules) {
        if (this.validateRule(rule)) {
          // 检查是否已存在
          const exists = this.localRules.some(r => 
            r.senderEmail === rule.senderEmail && r.patternType === rule.patternType
          );

          if (!exists) {
            const newRule = {
              ...rule,
              id: this.generateRuleId(),
              importedTime: new Date().toISOString(),
              isLocal: true,
              isShared: false
            };
            this.localRules.push(newRule);
            imported++;
          } else {
            skipped++;
          }
        } else {
          skipped++;
        }
      }

      if (imported > 0) {
        await storageManager.saveLocalRules(this.localRules);
        this.updateRuleCache();
      }

      return {
        success: true,
        message: `导入完成: ${imported} 条成功, ${skipped} 条跳过`,
        imported,
        skipped
      };
    } catch (error) {
      console.error('导入规则失败:', error);
      return { success: false, message: '导入失败: ' + error.message };
    }
  }
}

// 创建全局实例
export const ruleManager = new RuleManager();
