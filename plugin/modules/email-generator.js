/**
 * 邮件生成器模块
 * 负责生成临时邮箱地址和管理用户会话
 */

// 配置常量
const CONFIG = {
  DOMAIN: 'chaiyuangungunlai.dpdns.org',
  MIN_LENGTH: 8,
  MAX_LENGTH: 12,
  CHARACTERS: 'abcdefghijklmnopqrstuvwxyz0123456789'
};

/**
 * 邮件生成器类
 */
export class EmailGenerator {
  constructor() {
    this.currentSession = null;
    this.sessionHistory = [];
  }

  /**
   * 生成随机字符串
   * @param {number} length - 字符串长度
   * @returns {string} 随机字符串
   */
  generateRandomString(length) {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += CONFIG.CHARACTERS.charAt(Math.floor(Math.random() * CONFIG.CHARACTERS.length));
    }
    return result;
  }

  /**
   * 生成用户ID
   * @returns {string} 唯一用户ID
   */
  generateUserId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    return `user_${timestamp}_${random}`;
  }

  /**
   * 生成临时邮箱地址
   * @returns {Object} 包含邮箱信息的对象
   */
  generateTempEmail() {
    // 生成随机长度（8-12位）
    const length = Math.floor(Math.random() * (CONFIG.MAX_LENGTH - CONFIG.MIN_LENGTH + 1)) + CONFIG.MIN_LENGTH;
    
    // 生成随机字符串
    const randomString = this.generateRandomString(length);
    
    // 拼接完整邮箱地址
    const tempEmail = `${randomString}@${CONFIG.DOMAIN}`;
    
    // 生成用户ID
    const userId = this.generateUserId();
    
    // 记录生成时间 (格式: yyyy-MM-dd HH:mm:ss)
    const generateTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

    // 创建会话信息
    this.currentSession = {
      userId,
      tempEmail,
      generateTime,
      timestamp: Date.now(),
      isActive: true
    };

    return this.currentSession;
  }

  /**
   * 获取当前会话信息
   * @returns {Object|null} 当前会话信息
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * 设置当前会话
   * @param {Object} session - 会话信息
   */
  setCurrentSession(session) {
    this.currentSession = session;
  }

  /**
   * 清除当前会话
   */
  clearCurrentSession() {
    if (this.currentSession) {
      this.currentSession.isActive = false;
      this.sessionHistory.push(this.currentSession);
    }
    this.currentSession = null;
  }

  /**
   * 验证邮箱地址格式
   * @param {string} email - 邮箱地址
   * @returns {boolean} 是否有效
   */
  validateEmail(email) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  }

  /**
   * 检查邮箱是否属于当前域名
   * @param {string} email - 邮箱地址
   * @returns {boolean} 是否属于当前域名
   */
  isOurDomain(email) {
    return email.endsWith(`@${CONFIG.DOMAIN}`);
  }

  /**
   * 获取会话历史
   * @returns {Array} 会话历史数组
   */
  getSessionHistory() {
    return this.sessionHistory;
  }

  /**
   * 获取会话统计信息
   * @returns {Object} 统计信息
   */
  getSessionStats() {
    const total = this.sessionHistory.length + (this.currentSession ? 1 : 0);
    const active = this.currentSession ? 1 : 0;
    const expired = this.sessionHistory.length;

    return {
      total,
      active,
      expired,
      currentSession: this.currentSession
    };
  }

  /**
   * 从存储中恢复会话
   * @param {Object} sessionData - 存储的会话数据
   */
  restoreFromStorage(sessionData) {
    if (sessionData.currentSession) {
      this.currentSession = sessionData.currentSession;
    }
    if (sessionData.sessionHistory) {
      this.sessionHistory = sessionData.sessionHistory;
    }
  }

  /**
   * 导出会话数据用于存储
   * @returns {Object} 可存储的会话数据
   */
  exportForStorage() {
    return {
      currentSession: this.currentSession,
      sessionHistory: this.sessionHistory,
      timestamp: Date.now()
    };
  }

  /**
   * 检查会话是否过期
   * @param {Object} session - 会话对象
   * @param {number} expireHours - 过期小时数，默认24小时
   * @returns {boolean} 是否过期
   */
  isSessionExpired(session, expireHours = 24) {
    if (!session || !session.timestamp) {
      return true;
    }
    
    const now = Date.now();
    const expireTime = expireHours * 60 * 60 * 1000; // 转换为毫秒
    
    return (now - session.timestamp) > expireTime;
  }

  /**
   * 清理过期会话
   * @param {number} expireHours - 过期小时数，默认24小时
   */
  cleanExpiredSessions(expireHours = 24) {
    // 检查当前会话是否过期
    if (this.currentSession && this.isSessionExpired(this.currentSession, expireHours)) {
      this.clearCurrentSession();
    }

    // 清理历史会话中的过期项
    this.sessionHistory = this.sessionHistory.filter(session => 
      !this.isSessionExpired(session, expireHours * 7) // 历史会话保留更长时间
    );
  }
}

// 创建全局实例
export const emailGenerator = new EmailGenerator();
