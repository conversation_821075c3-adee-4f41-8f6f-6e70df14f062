/**
 * API客户端模块
 * 负责与后端服务器的通信
 */

/**
 * API客户端类
 */
export class ApiClient {
  constructor() {
    this.baseUrl = 'http://1.12.224.176:8080/api';
    this.apiKey = 'mailplugin_api_key_2024';
    this.timeout = 10000; // 10秒超时
  }

  /**
   * 发送HTTP请求
   * @param {string} endpoint - API端点
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async request(endpoint, options = {}) {
    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
    
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: this.timeout
    };

    const requestOptions = { ...defaultOptions, ...options };

    // 添加API密钥到请求体或查询参数
    if (requestOptions.method === 'POST' || requestOptions.method === 'PUT') {
      if (requestOptions.data) {
        requestOptions.data.apiKey = this.apiKey;
        requestOptions.body = JSON.stringify(requestOptions.data);
      }
    } else {
      // GET请求，添加到URL参数
      const separator = url.includes('?') ? '&' : '?';
      const finalUrl = `${url}${separator}apiKey=${encodeURIComponent(this.apiKey)}`;
      return this.fetchWithTimeout(finalUrl, requestOptions);
    }

    return this.fetchWithTimeout(url, requestOptions);
  }

  /**
   * 带超时的fetch请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async fetchWithTimeout(url, options) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('请求超时');
      }
      
      throw error;
    }
  }

  /**
   * 生成临时邮箱
   * @param {string} tempEmail - 临时邮箱地址
   * @param {string} generateTime - 生成时间
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 生成结果
   */
  async generateEmail(tempEmail, generateTime, userId) {
    return await this.request('/generate-email', {
      method: 'POST',
      data: {
        tempEmail: tempEmail,
        generateTime: generateTime,
        userId: userId
      }
    });
  }

  /**
   * 获取邮件列表
   * @param {string} userId - 用户ID
   * @returns {Promise<Array>} 邮件列表
   */
  async getEmails(userId) {
    return await this.request(`/get-emails?userId=${encodeURIComponent(userId)}`);
  }

  /**
   * 获取共享解析规则
   * @returns {Promise<Array>} 解析规则列表
   */
  async getSharedRules() {
    return await this.request('/get-shared-rules');
  }

  /**
   * 上传共享解析规则
   * @param {Object} rule - 解析规则对象
   * @returns {Promise<Object>} 上传结果
   */
  async uploadSharedRule(rule) {
    return await this.request('/upload-shared-rule', {
      method: 'POST',
      data: {
        rule: rule
      }
    });
  }

  /**
   * 更新解析规则
   * @param {Object} rule - 解析规则对象
   * @returns {Promise<Object>} 更新结果
   */
  async updateRule(rule) {
    return await this.request('/update-rule', {
      method: 'PUT',
      data: {
        rule: rule
      }
    });
  }

  /**
   * 删除解析规则
   * @param {string} ruleId - 规则ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteRule(ruleId) {
    return await this.request('/delete-rule', {
      method: 'DELETE',
      data: {
        ruleId: ruleId
      }
    });
  }

  /**
   * 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    try {
      return await this.request('/health');
    } catch (error) {
      return { 
        success: false, 
        message: '服务器连接失败: ' + error.message 
      };
    }
  }

  /**
   * 获取系统状态
   * @returns {Promise<Object>} 系统状态
   */
  async getSystemStatus() {
    try {
      return await this.request('/status');
    } catch (error) {
      return { 
        success: false, 
        message: '获取系统状态失败: ' + error.message 
      };
    }
  }
}

// 创建全局实例
export const apiClient = new ApiClient();
