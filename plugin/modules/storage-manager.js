/**
 * 存储管理器模块
 * 负责本地数据的存储和管理
 */

/**
 * 存储管理器类
 */
export class StorageManager {
  constructor() {
    this.storageKeys = {
      USER_SESSIONS: 'userSessions',
      LOCAL_RULES: 'localRules',
      SETTINGS: 'settings',
      EMAIL_CACHE: 'emailCache',
      RULE_CACHE: 'ruleCache'
    };
  }

  /**
   * 保存数据到Chrome存储
   * @param {string} key - 存储键
   * @param {*} data - 要保存的数据
   * @returns {Promise<boolean>} 是否保存成功
   */
  async save(key, data) {
    try {
      await chrome.storage.local.set({ [key]: data });
      console.log(`数据已保存到存储: ${key}`);
      return true;
    } catch (error) {
      console.error(`保存数据失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 从Chrome存储读取数据
   * @param {string} key - 存储键
   * @param {*} defaultValue - 默认值
   * @returns {Promise<*>} 读取的数据
   */
  async load(key, defaultValue = null) {
    try {
      const result = await chrome.storage.local.get([key]);
      return result[key] !== undefined ? result[key] : defaultValue;
    } catch (error) {
      console.error(`读取数据失败: ${key}`, error);
      return defaultValue;
    }
  }

  /**
   * 删除存储中的数据
   * @param {string} key - 存储键
   * @returns {Promise<boolean>} 是否删除成功
   */
  async remove(key) {
    try {
      await chrome.storage.local.remove([key]);
      console.log(`数据已从存储中删除: ${key}`);
      return true;
    } catch (error) {
      console.error(`删除数据失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 清空所有存储数据
   * @returns {Promise<boolean>} 是否清空成功
   */
  async clear() {
    try {
      await chrome.storage.local.clear();
      console.log('所有存储数据已清空');
      return true;
    } catch (error) {
      console.error('清空存储数据失败:', error);
      return false;
    }
  }

  /**
   * 获取存储使用情况
   * @returns {Promise<Object>} 存储使用情况
   */
  async getStorageInfo() {
    try {
      const bytesInUse = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      
      return {
        used: bytesInUse,
        quota: quota,
        available: quota - bytesInUse,
        usagePercent: (bytesInUse / quota * 100).toFixed(2)
      };
    } catch (error) {
      console.error('获取存储信息失败:', error);
      return null;
    }
  }

  // === 用户会话相关方法 ===

  /**
   * 保存用户会话
   * @param {Object} sessionData - 会话数据
   * @returns {Promise<boolean>} 是否保存成功
   */
  async saveUserSession(sessionData) {
    return await this.save(this.storageKeys.USER_SESSIONS, sessionData);
  }

  /**
   * 加载用户会话
   * @returns {Promise<Object>} 会话数据
   */
  async loadUserSession() {
    return await this.load(this.storageKeys.USER_SESSIONS, {});
  }

  /**
   * 清除用户会话
   * @returns {Promise<boolean>} 是否清除成功
   */
  async clearUserSession() {
    return await this.remove(this.storageKeys.USER_SESSIONS);
  }

  // === 本地规则相关方法 ===

  /**
   * 保存本地解析规则
   * @param {Array} rules - 规则数组
   * @returns {Promise<boolean>} 是否保存成功
   */
  async saveLocalRules(rules) {
    return await this.save(this.storageKeys.LOCAL_RULES, rules);
  }

  /**
   * 加载本地解析规则
   * @returns {Promise<Array>} 规则数组
   */
  async loadLocalRules() {
    return await this.load(this.storageKeys.LOCAL_RULES, []);
  }

  /**
   * 添加本地规则
   * @param {Object} rule - 规则对象
   * @returns {Promise<boolean>} 是否添加成功
   */
  async addLocalRule(rule) {
    try {
      const rules = await this.loadLocalRules();
      rules.push({
        ...rule,
        id: this.generateRuleId(),
        createdTime: new Date().toISOString(),
        isLocal: true
      });
      return await this.saveLocalRules(rules);
    } catch (error) {
      console.error('添加本地规则失败:', error);
      return false;
    }
  }

  /**
   * 删除本地规则
   * @param {string} ruleId - 规则ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async removeLocalRule(ruleId) {
    try {
      const rules = await this.loadLocalRules();
      const filteredRules = rules.filter(rule => rule.id !== ruleId);
      return await this.saveLocalRules(filteredRules);
    } catch (error) {
      console.error('删除本地规则失败:', error);
      return false;
    }
  }

  // === 设置相关方法 ===

  /**
   * 保存设置
   * @param {Object} settings - 设置对象
   * @returns {Promise<boolean>} 是否保存成功
   */
  async saveSettings(settings) {
    return await this.save(this.storageKeys.SETTINGS, settings);
  }

  /**
   * 加载设置
   * @returns {Promise<Object>} 设置对象
   */
  async loadSettings() {
    return await this.load(this.storageKeys.SETTINGS, {
      autoRefresh: true,
      refreshInterval: 5000,
      notifications: true,
      theme: 'light'
    });
  }

  // === 缓存相关方法 ===

  /**
   * 缓存邮件数据
   * @param {string} userId - 用户ID
   * @param {Array} emails - 邮件数组
   * @param {number} ttl - 缓存时间（分钟）
   * @returns {Promise<boolean>} 是否缓存成功
   */
  async cacheEmails(userId, emails, ttl = 5) {
    const cacheData = {
      userId,
      emails,
      timestamp: Date.now(),
      ttl: ttl * 60 * 1000 // 转换为毫秒
    };
    
    const cache = await this.load(this.storageKeys.EMAIL_CACHE, {});
    cache[userId] = cacheData;
    
    return await this.save(this.storageKeys.EMAIL_CACHE, cache);
  }

  /**
   * 获取缓存的邮件数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Array|null>} 缓存的邮件数据
   */
  async getCachedEmails(userId) {
    try {
      const cache = await this.load(this.storageKeys.EMAIL_CACHE, {});
      const cacheData = cache[userId];
      
      if (!cacheData) {
        return null;
      }
      
      // 检查缓存是否过期
      const now = Date.now();
      if (now - cacheData.timestamp > cacheData.ttl) {
        // 删除过期缓存
        delete cache[userId];
        await this.save(this.storageKeys.EMAIL_CACHE, cache);
        return null;
      }
      
      return cacheData.emails;
    } catch (error) {
      console.error('获取缓存邮件失败:', error);
      return null;
    }
  }

  /**
   * 清理过期缓存
   * @returns {Promise<boolean>} 是否清理成功
   */
  async cleanExpiredCache() {
    try {
      const cache = await this.load(this.storageKeys.EMAIL_CACHE, {});
      const now = Date.now();
      let hasChanges = false;
      
      for (const [userId, cacheData] of Object.entries(cache)) {
        if (now - cacheData.timestamp > cacheData.ttl) {
          delete cache[userId];
          hasChanges = true;
        }
      }
      
      if (hasChanges) {
        await this.save(this.storageKeys.EMAIL_CACHE, cache);
      }
      
      return true;
    } catch (error) {
      console.error('清理过期缓存失败:', error);
      return false;
    }
  }

  // === 工具方法 ===

  /**
   * 生成规则ID
   * @returns {string} 唯一规则ID
   */
  generateRuleId() {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 导出所有数据
   * @returns {Promise<Object>} 导出的数据
   */
  async exportAllData() {
    try {
      const allData = await chrome.storage.local.get();
      return {
        ...allData,
        exportTime: new Date().toISOString(),
        version: '1.0.0'
      };
    } catch (error) {
      console.error('导出数据失败:', error);
      return null;
    }
  }

  /**
   * 导入数据
   * @param {Object} data - 要导入的数据
   * @returns {Promise<boolean>} 是否导入成功
   */
  async importData(data) {
    try {
      // 移除元数据
      const { exportTime, version, ...importData } = data;
      
      await chrome.storage.local.set(importData);
      console.log('数据导入成功');
      return true;
    } catch (error) {
      console.error('导入数据失败:', error);
      return false;
    }
  }
}

// 创建全局实例
export const storageManager = new StorageManager();
