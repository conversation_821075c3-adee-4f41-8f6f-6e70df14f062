<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱助手</title>
    <link rel="stylesheet" href="../assets/styles/common.css">
    <link rel="stylesheet" href="sidepanel.css">
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <header class="header">
            <div class="logo">
                <img src="../icons/icon.png" alt="临时邮箱助手" class="logo-icon">
                <h1 class="title">临时邮箱助手</h1>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">就绪</span>
            </div>
        </header>

        <!-- 邮箱生成区域 -->
        <section class="email-generator">
            <div class="generator-card">
                <h2>临时邮箱生成</h2>
                <div class="generator-content">
                    <div class="email-display" id="emailDisplay">
                        <input type="text" id="tempEmailInput" placeholder="点击生成临时邮箱" readonly>
                        <button class="copy-btn" id="copyEmailBtn" title="复制邮箱地址">
                            <span class="copy-icon">📋</span>
                        </button>
                    </div>
                    <div class="generator-actions">
                        <button class="generate-btn" id="generateBtn">
                            <span class="btn-icon">✨</span>
                            生成新邮箱
                        </button>
                        <button class="refresh-btn" id="refreshBtn" disabled>
                            <span class="btn-icon">🔄</span>
                            刷新邮件
                        </button>
                    </div>
                    <div class="generator-info">
                        <small id="generatorStatus">点击"生成新邮箱"开始使用</small>
                    </div>
                </div>
            </div>
        </section>

        <!-- 邮件列表区域 -->
        <section class="email-list">
            <div class="list-header">
                <h2>收到的邮件</h2>
                <div class="list-controls">
                    <span class="email-count" id="emailCount">0 封邮件</span>
                    <button class="auto-refresh-toggle" id="autoRefreshToggle" title="自动刷新">
                        <span class="toggle-icon">⏸️</span>
                    </button>
                </div>
            </div>
            <div class="list-content" id="emailListContent">
                <div class="empty-list">
                    <div class="empty-icon">📭</div>
                    <p>暂无邮件</p>
                    <small>生成临时邮箱后，收到的邮件将显示在这里</small>
                </div>
            </div>
        </section>

        <!-- 解析规则管理区域 -->
        <section class="rule-management">
            <div class="rule-card">
                <h2>解析规则管理</h2>
                <div class="rule-tabs">
                    <button class="tab-btn active" data-tab="local">本地规则</button>
                    <button class="tab-btn" data-tab="shared">共享规则</button>
                    <button class="tab-btn" data-tab="create">创建规则</button>
                </div>
                
                <div class="tab-content" id="localRulesTab">
                    <div class="rule-list" id="localRulesList">
                        <div class="empty-rules">
                            <p>暂无本地规则</p>
                            <small>创建规则来自动提取验证码</small>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="sharedRulesTab" style="display: none;">
                    <div class="rule-list" id="sharedRulesList">
                        <div class="loading-rules">
                            <div class="loading-spinner"></div>
                            <p>加载共享规则中...</p>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="createRuleTab" style="display: none;">
                    <div class="rule-creator">
                        <form id="ruleCreatorForm">
                            <div class="form-group">
                                <label for="rulePlatform">平台名称</label>
                                <input type="text" id="rulePlatform" placeholder="例如：GitHub" required>
                            </div>
                            <div class="form-group">
                                <label for="ruleSender">发件人邮箱</label>
                                <input type="email" id="ruleSender" placeholder="例如：<EMAIL>" required>
                            </div>
                            <div class="form-group">
                                <label for="ruleExample">邮件示例</label>
                                <textarea id="ruleExample" placeholder="粘贴完整的邮件内容..." rows="6" required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="rulePattern">提取模式</label>
                                <select id="rulePatternType" required>
                                    <option value="">选择提取类型</option>
                                    <option value="CODE">验证码</option>
                                    <option value="LINK">链接</option>
                                </select>
                                <input type="text" id="rulePattern" placeholder="正则表达式或示例值" required>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="test-btn" id="testRuleBtn">测试规则</button>
                                <button type="button" class="save-local-btn" id="saveLocalBtn">保存到本地</button>
                                <button type="button" class="share-btn" id="shareRuleBtn">共享规则</button>
                            </div>
                        </form>
                        <div class="test-result" id="testResult" style="display: none;">
                            <h4>测试结果</h4>
                            <div class="result-content" id="testResultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 加载脚本 -->
    <script type="module" src="sidepanel.js"></script>
</body>
</html>
