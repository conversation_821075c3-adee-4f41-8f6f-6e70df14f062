/* 侧边栏主界面样式 */

/* 容器和布局 */
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 头部区域 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #28a745;
}

.status-dot.loading {
  background: #ffc107;
  animation: pulse 1.5s infinite;
}

.status-dot.error {
  background: #dc3545;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 邮箱生成区域 */
.email-generator {
  padding: 20px;
}

.generator-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.generator-card h2 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.email-display {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

#tempEmailInput {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  background: #f8f9fa;
  color: #495057;
}

#tempEmailInput:focus {
  outline: none;
  border-color: #007bff;
  background: #fff;
}

.copy-btn {
  padding: 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.copy-btn:hover {
  background: #5a6268;
}

.generator-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.generate-btn, .refresh-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.generate-btn {
  background: #007bff;
  color: white;
}

.generate-btn:hover:not(:disabled) {
  background: #0056b3;
}

.generate-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.refresh-btn {
  background: #28a745;
  color: white;
}

.refresh-btn:hover:not(:disabled) {
  background: #1e7e34;
}

.refresh-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.generator-info {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
}

/* 邮件列表区域 */
.email-list {
  flex: 1;
  padding: 0 20px 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.list-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.email-count {
  font-size: 14px;
  color: #6c757d;
}

.auto-refresh-toggle {
  padding: 8px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.auto-refresh-toggle:hover {
  background: #e9ecef;
}

.list-content {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 空列表状态 */
.empty-list {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-list p {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.empty-list small {
  font-size: 14px;
  opacity: 0.8;
}

/* 邮件项 */
.email-item {
  border-bottom: 1px solid #e9ecef;
}

.email-item:last-child {
  border-bottom: none;
}

.email-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.email-header:hover {
  background: #f8f9fa;
}

.email-info {
  flex: 1;
}

.email-from {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.email-subject {
  font-size: 14px;
  color: #495057;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.email-time {
  font-size: 12px;
  color: #6c757d;
}

.email-indicators {
  display: flex;
  align-items: center;
  gap: 8px;
}

.has-results {
  font-size: 16px;
}

.toggle-icon {
  font-size: 12px;
  color: #6c757d;
  transition: transform 0.2s;
}

/* 邮件详情 */
.email-detail {
  padding: 0 20px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.email-results {
  margin-bottom: 20px;
}

.result-section {
  margin-bottom: 16px;
}

.result-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 8px;
}

.result-value {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: 600;
  color: #007bff;
}

.result-link {
  flex: 1;
  color: #007bff;
  text-decoration: none;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-link:hover {
  text-decoration: underline;
}

.result-item .copy-btn {
  padding: 4px 8px;
  font-size: 12px;
  background: #28a745;
}

.result-info {
  font-size: 12px;
  color: #6c757d;
}

.no-results {
  padding: 20px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.email-content h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.content-text {
  padding: 12px;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.5;
  color: #495057;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 规则管理区域 */
.rule-management {
  padding: 0 20px 20px;
}

.rule-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.rule-card h2 {
  margin: 0;
  padding: 20px 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

/* 规则标签 */
.rule-tabs {
  display: flex;
  padding: 16px 20px 0;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-size: 14px;
  color: #6c757d;
  transition: all 0.2s;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.tab-btn:hover {
  color: #007bff;
}

/* 标签内容 */
.tab-content {
  padding: 20px;
  min-height: 200px;
}

/* 规则列表 */
.rule-list {
  max-height: 300px;
  overflow-y: auto;
}

.empty-rules {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.empty-rules p {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.empty-rules small {
  font-size: 14px;
  opacity: 0.8;
}

.loading-rules {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 规则项 */
.rule-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.2s;
}

.rule-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.rule-item.inactive {
  opacity: 0.6;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
}

.rule-info {
  flex: 1;
}

.rule-platform {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.rule-sender {
  font-size: 13px;
  color: #495057;
  margin-bottom: 2px;
}

.rule-type {
  font-size: 12px;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.rule-actions {
  display: flex;
  gap: 8px;
}

.toggle-rule-btn, .delete-rule-btn {
  padding: 4px 8px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.toggle-rule-btn:hover, .delete-rule-btn:hover {
  background: #e9ecef;
}

.rule-details {
  padding: 12px 16px;
  background: #fff;
  border-top: 1px solid #e9ecef;
}

.rule-pattern {
  font-size: 13px;
  color: #495057;
  font-family: 'Courier New', monospace;
  margin-bottom: 4px;
  word-break: break-all;
}

.rule-time {
  font-size: 12px;
  color: #6c757d;
}

/* 规则创建器 */
.rule-creator {
  max-width: 100%;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.test-btn, .save-local-btn, .share-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-btn {
  background: #6c757d;
  color: white;
}

.test-btn:hover {
  background: #5a6268;
}

.save-local-btn {
  background: #28a745;
  color: white;
}

.save-local-btn:hover {
  background: #1e7e34;
}

.share-btn {
  background: #007bff;
  color: white;
}

.share-btn:hover {
  background: #0056b3;
}

/* 测试结果 */
.test-result {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.test-result h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.test-status {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 12px;
}

.test-status.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.test-status.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.test-matches h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
}

.test-matches ul {
  margin: 0;
  padding-left: 20px;
}

.test-matches li {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #495057;
  margin-bottom: 4px;
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.notification.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.notification.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 400px) {
  .header {
    padding: 12px 16px;
  }

  .email-generator,
  .email-list,
  .rule-management {
    padding-left: 16px;
    padding-right: 16px;
  }

  .generator-actions {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }
}
