/**
 * 侧边栏主界面逻辑
 * 负责用户界面交互和功能协调
 */

import { emailGenerator } from '../modules/email-generator.js';
import { emailParser } from '../modules/email-parser.js';
import { ruleManager } from '../modules/rule-manager.js';
import { storageManager } from '../modules/storage-manager.js';

/**
 * 侧边栏应用类
 */
class SidePanelApp {
  constructor() {
    this.currentSession = null;
    this.emails = [];
    this.isPolling = false;
    this.pollingInterval = null;
    
    // DOM元素引用
    this.elements = {};
    
    // 状态管理
    this.state = {
      isGenerating: false,
      isRefreshing: false,
      autoRefresh: true
    };
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      console.log('初始化侧边栏应用...');
      
      // 初始化DOM元素引用
      this.initElements();
      
      // 初始化事件监听器
      this.initEventListeners();
      
      // 初始化模块
      await this.initModules();
      
      // 恢复会话状态
      await this.restoreSession();
      
      // 更新UI状态
      this.updateUI();
      
      console.log('侧边栏应用初始化完成');
    } catch (error) {
      console.error('初始化失败:', error);
      this.showError('应用初始化失败: ' + error.message);
    }
  }

  /**
   * 初始化DOM元素引用
   */
  initElements() {
    this.elements = {
      // 状态指示器
      statusIndicator: document.getElementById('statusIndicator'),
      
      // 邮箱生成区域
      tempEmailInput: document.getElementById('tempEmailInput'),
      copyEmailBtn: document.getElementById('copyEmailBtn'),
      generateBtn: document.getElementById('generateBtn'),
      refreshBtn: document.getElementById('refreshBtn'),
      generatorStatus: document.getElementById('generatorStatus'),
      
      // 邮件列表区域
      emailCount: document.getElementById('emailCount'),
      autoRefreshToggle: document.getElementById('autoRefreshToggle'),
      emailListContent: document.getElementById('emailListContent'),
      
      // 规则管理区域
      localRulesList: document.getElementById('localRulesList'),
      sharedRulesList: document.getElementById('sharedRulesList'),
      ruleCreatorForm: document.getElementById('ruleCreatorForm'),
      testResult: document.getElementById('testResult'),
      testResultContent: document.getElementById('testResultContent')
    };
  }

  /**
   * 初始化事件监听器
   */
  initEventListeners() {
    // 生成邮箱按钮
    this.elements.generateBtn.addEventListener('click', () => this.handleGenerateEmail());
    
    // 刷新邮件按钮
    this.elements.refreshBtn.addEventListener('click', () => this.handleRefreshEmails());
    
    // 复制邮箱按钮
    this.elements.copyEmailBtn.addEventListener('click', () => this.handleCopyEmail());
    
    // 自动刷新切换
    this.elements.autoRefreshToggle.addEventListener('click', () => this.toggleAutoRefresh());
    
    // 邮件列表事件委托
    this.elements.emailListContent.addEventListener('click', (e) => {
      // 处理邮件展开/折叠
      const emailHeader = e.target.closest('[data-toggle-email]');
      if (emailHeader) {
        const messageId = emailHeader.getAttribute('data-toggle-email');
        this.toggleEmailDetail(messageId);
        return;
      }

      // 处理复制按钮
      const copyBtn = e.target.closest('[data-copy-text]');
      if (copyBtn) {
        const text = copyBtn.getAttribute('data-copy-text');
        const message = copyBtn.getAttribute('data-copy-message');
        this.copyText(text, message);
        return;
      }
    });

    // 规则管理标签切换
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
    });

    // 规则创建表单
    if (this.elements.ruleCreatorForm) {
      document.getElementById('testRuleBtn')?.addEventListener('click', () => this.testRule());
      document.getElementById('saveLocalBtn')?.addEventListener('click', () => this.saveLocalRule());
      document.getElementById('shareRuleBtn')?.addEventListener('click', () => this.shareRule());
    }

    // 监听来自service worker的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'emailsUpdated') {
        this.handleEmailsUpdate(message.data);
      }
    });
  }

  /**
   * 初始化模块
   */
  async initModules() {
    await ruleManager.initialize();
    await this.loadRules();
  }

  /**
   * 恢复会话状态
   */
  async restoreSession() {
    try {
      const sessionData = await storageManager.loadUserSession();
      if (sessionData.currentSession) {
        this.currentSession = sessionData.currentSession;
        emailGenerator.setCurrentSession(this.currentSession);
        
        // 更新UI显示
        this.elements.tempEmailInput.value = this.currentSession.tempEmail;
        this.elements.refreshBtn.disabled = false;
        this.elements.generatorStatus.textContent = `邮箱生成于 ${new Date(this.currentSession.generateTime).toLocaleString()}`;
        
        // 开始轮询
        if (this.state.autoRefresh) {
          this.startPolling();
        }
        
        // 立即刷新一次邮件
        await this.refreshEmails();
      }
    } catch (error) {
      console.error('恢复会话失败:', error);
    }
  }

  /**
   * 处理生成邮箱
   */
  async handleGenerateEmail() {
    if (this.state.isGenerating) return;
    
    try {
      this.state.isGenerating = true;
      this.updateGenerateButton(true);
      this.updateStatus('生成中...', 'loading');
      
      // 停止当前轮询
      this.stopPolling();
      
      // 生成新邮箱
      const session = emailGenerator.generateTempEmail();
      
      // 发送到后端
      const response = await this.sendMessage('generateEmail', session);
      
      if (response.success) {
        this.currentSession = session;
        
        // 保存会话
        await storageManager.saveUserSession({ currentSession: session });
        
        // 更新UI
        this.elements.tempEmailInput.value = session.tempEmail;
        this.elements.refreshBtn.disabled = false;
        this.elements.generatorStatus.textContent = `邮箱生成成功，等待邮件...`;
        
        // 清空邮件列表
        this.emails = [];
        this.updateEmailList();
        
        // 开始轮询
        if (this.state.autoRefresh) {
          this.startPolling();
        }
        
        this.updateStatus('就绪', 'ready');
        this.showSuccess('临时邮箱生成成功！');
      } else {
        throw new Error(response.message || '生成失败');
      }
    } catch (error) {
      console.error('生成邮箱失败:', error);
      this.showError('生成邮箱失败: ' + error.message);
      this.updateStatus('错误', 'error');
    } finally {
      this.state.isGenerating = false;
      this.updateGenerateButton(false);
    }
  }

  /**
   * 处理刷新邮件
   */
  async handleRefreshEmails() {
    if (!this.currentSession || this.state.isRefreshing) return;
    
    await this.refreshEmails();
  }

  /**
   * 刷新邮件
   */
  async refreshEmails() {
    if (!this.currentSession) return;
    
    try {
      this.state.isRefreshing = true;
      this.updateRefreshButton(true);
      this.updateStatus('获取邮件中...', 'loading');
      
      // 获取邮件
      const response = await this.sendMessage('getEmails', { userId: this.currentSession.userId });
      
      if (response.success && response.emails) {
        this.emails = response.emails;
        
        // 解析邮件内容
        await this.parseEmails();
        
        // 更新UI
        this.updateEmailList();
        this.updateEmailCount();
        
        this.updateStatus('就绪', 'ready');
      } else {
        console.warn('获取邮件失败:', response.message);
      }
    } catch (error) {
      console.error('刷新邮件失败:', error);
      this.showError('刷新邮件失败: ' + error.message);
      this.updateStatus('错误', 'error');
    } finally {
      this.state.isRefreshing = false;
      this.updateRefreshButton(false);
    }
  }

  /**
   * 解析邮件内容
   */
  async parseEmails() {
    const allRules = ruleManager.getAllRules();
    
    for (const email of this.emails) {
      if (!email.parsed) {
        console.log(`解析邮件: ${email.from} - ${email.subject}`);
        
        const parseResults = await emailParser.extractFromEmail(email, allRules);
        
        // 将解析结果附加到邮件对象
        email.parsed = {
          codes: parseResults.codes || [],
          links: parseResults.links || [],
          emails: parseResults.emails || [],
          timestamp: Date.now()
        };
        
        console.log(`解析完成 - 验证码: ${parseResults.codes.length}, 链接: ${parseResults.links.length}`);
      }
    }
  }

  /**
   * 更新邮件列表显示
   */
  updateEmailList() {
    if (this.emails.length === 0) {
      this.elements.emailListContent.innerHTML = `
        <div class="empty-list">
          <div class="empty-icon">📭</div>
          <p>暂无邮件</p>
          <small>生成临时邮箱后，收到的邮件将显示在这里</small>
        </div>
      `;
      return;
    }

    const emailsHtml = this.emails.map(email => this.createEmailItem(email)).join('');
    this.elements.emailListContent.innerHTML = emailsHtml;
  }

  /**
   * 创建邮件项HTML
   */
  createEmailItem(email) {
    const parsed = email.parsed || { codes: [], links: [], emails: [] };
    const hasResults = parsed.codes.length > 0 || parsed.links.length > 0;
    
    return `
      <div class="email-item" data-message-id="${email.messageId}">
        <div class="email-header" data-toggle-email="${email.messageId}">
          <div class="email-info">
            <div class="email-from">${this.escapeHtml(email.from)}</div>
            <div class="email-subject">${this.escapeHtml(email.subject)}</div>
            <div class="email-time">${new Date(email.receivedTime).toLocaleString()}</div>
          </div>
          <div class="email-indicators">
            ${hasResults ? '<span class="has-results">🎯</span>' : ''}
            <span class="toggle-icon">▼</span>
          </div>
        </div>
        <div class="email-detail" style="display: none;">
          ${this.createEmailResults(parsed)}
          <div class="email-content">
            <h4>邮件内容</h4>
            <div class="content-text">${this.escapeHtml(email.content || '无内容')}</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 创建邮件解析结果HTML
   */
  createEmailResults(parsed) {
    let resultsHtml = '<div class="email-results">';
    
    // 验证码结果
    if (parsed.codes.length > 0) {
      resultsHtml += '<div class="result-section"><h4>验证码</h4>';
      parsed.codes.forEach(code => {
        resultsHtml += `
          <div class="result-item">
            <span class="result-value">${this.escapeHtml(code.value)}</span>
            <button class="copy-btn" data-copy-text="${this.escapeHtml(code.value)}" data-copy-message="验证码已复制">复制</button>
            <small class="result-info">置信度: ${(code.confidence * 100).toFixed(0)}%</small>
          </div>
        `;
      });
      resultsHtml += '</div>';
    }
    
    // 链接结果
    if (parsed.links.length > 0) {
      resultsHtml += '<div class="result-section"><h4>链接</h4>';
      parsed.links.forEach(link => {
        resultsHtml += `
          <div class="result-item">
            <a href="${this.escapeHtml(link.value)}" target="_blank" class="result-link">${this.escapeHtml(link.value)}</a>
            <button class="copy-btn" data-copy-text="${this.escapeHtml(link.value)}" data-copy-message="链接已复制">复制</button>
          </div>
        `;
      });
      resultsHtml += '</div>';
    }
    
    // 如果没有解析结果
    if (parsed.codes.length === 0 && parsed.links.length === 0) {
      resultsHtml += '<div class="no-results"><p>未找到验证码或链接</p></div>';
    }
    
    resultsHtml += '</div>';
    return resultsHtml;
  }

  /**
   * 切换邮件详情显示
   */
  toggleEmailDetail(messageId) {
    const emailItem = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!emailItem) return;

    const detail = emailItem.querySelector('.email-detail');
    const toggleIcon = emailItem.querySelector('.toggle-icon');

    if (detail.style.display === 'none') {
      detail.style.display = 'block';
      toggleIcon.textContent = '▲';
    } else {
      detail.style.display = 'none';
      toggleIcon.textContent = '▼';
    }
  }

  /**
   * 处理复制邮箱
   */
  async handleCopyEmail() {
    if (!this.currentSession) return;

    await this.copyText(this.currentSession.tempEmail, '邮箱地址已复制');
  }

  /**
   * 复制文本到剪贴板
   */
  async copyText(text, message = '已复制') {
    try {
      await navigator.clipboard.writeText(text);
      this.showSuccess(message);
    } catch (error) {
      console.error('复制失败:', error);
      this.showError('复制失败');
    }
  }

  /**
   * 切换自动刷新
   */
  toggleAutoRefresh() {
    this.state.autoRefresh = !this.state.autoRefresh;

    if (this.state.autoRefresh) {
      this.elements.autoRefreshToggle.querySelector('.toggle-icon').textContent = '⏸️';
      if (this.currentSession) {
        this.startPolling();
      }
    } else {
      this.elements.autoRefreshToggle.querySelector('.toggle-icon').textContent = '▶️';
      this.stopPolling();
    }
  }

  /**
   * 开始轮询
   */
  startPolling() {
    if (this.isPolling || !this.currentSession) return;

    this.isPolling = true;
    this.sendMessage('startPolling', { userId: this.currentSession.userId });
  }

  /**
   * 停止轮询
   */
  stopPolling() {
    if (!this.isPolling || !this.currentSession) return;

    this.isPolling = false;
    this.sendMessage('stopPolling', { userId: this.currentSession.userId });
  }

  /**
   * 处理邮件更新
   */
  async handleEmailsUpdate(data) {
    if (data.success && data.emails) {
      this.emails = data.emails;
      await this.parseEmails();
      this.updateEmailList();
      this.updateEmailCount();

      // 显示通知
      if (data.emails.length > 0) {
        this.showSuccess(`收到 ${data.emails.length} 封新邮件`);
      }
    }
  }

  /**
   * 发送消息到service worker
   */
  async sendMessage(action, data = {}) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ action, data }, resolve);
    });
  }

  /**
   * 更新UI状态
   */
  updateUI() {
    this.updateEmailCount();
    this.updateStatus('就绪', 'ready');
  }

  /**
   * 更新邮件计数
   */
  updateEmailCount() {
    const count = this.emails.length;
    this.elements.emailCount.textContent = `${count} 封邮件`;
  }

  /**
   * 更新状态指示器
   */
  updateStatus(text, type = 'ready') {
    const statusText = this.elements.statusIndicator.querySelector('.status-text');
    const statusDot = this.elements.statusIndicator.querySelector('.status-dot');

    statusText.textContent = text;
    statusDot.className = `status-dot ${type}`;
  }

  /**
   * 更新生成按钮状态
   */
  updateGenerateButton(isLoading) {
    this.elements.generateBtn.disabled = isLoading;
    this.elements.generateBtn.innerHTML = isLoading
      ? '<span class="btn-icon">⏳</span>生成中...'
      : '<span class="btn-icon">✨</span>生成新邮箱';
  }

  /**
   * 更新刷新按钮状态
   */
  updateRefreshButton(isLoading) {
    this.elements.refreshBtn.disabled = isLoading;
    this.elements.refreshBtn.innerHTML = isLoading
      ? '<span class="btn-icon">⏳</span>刷新中...'
      : '<span class="btn-icon">🔄</span>刷新邮件';
  }

  /**
   * 显示成功消息
   */
  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  /**
   * 显示错误消息
   */
  showError(message) {
    this.showNotification(message, 'error');
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // 添加到页面
    document.body.appendChild(notification);

    // 自动移除
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  /**
   * HTML转义
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 切换规则管理标签
   */
  switchTab(tabName) {
    // 更新标签按钮状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // 显示对应内容
    document.querySelectorAll('.tab-content').forEach(content => {
      content.style.display = content.id === `${tabName}RulesTab` ? 'block' : 'none';
    });

    // 加载对应数据
    if (tabName === 'local') {
      this.loadLocalRules();
    } else if (tabName === 'shared') {
      this.loadSharedRules();
    }
  }

  /**
   * 加载规则
   */
  async loadRules() {
    await this.loadLocalRules();
    await this.loadSharedRules();
  }

  /**
   * 加载本地规则
   */
  async loadLocalRules() {
    const rules = ruleManager.getLocalRules();
    this.renderRuleList(rules, this.elements.localRulesList, true);
  }

  /**
   * 加载共享规则
   */
  async loadSharedRules() {
    const rules = ruleManager.getSharedRules();
    this.renderRuleList(rules, this.elements.sharedRulesList, false);
  }

  /**
   * 渲染规则列表
   */
  renderRuleList(rules, container, isLocal) {
    if (rules.length === 0) {
      container.innerHTML = `
        <div class="empty-rules">
          <p>暂无${isLocal ? '本地' : '共享'}规则</p>
          <small>${isLocal ? '创建规则来自动提取验证码' : '加载共享规则中...'}</small>
        </div>
      `;
      return;
    }

    const rulesHtml = rules.map(rule => this.createRuleItem(rule, isLocal)).join('');
    container.innerHTML = rulesHtml;
  }

  /**
   * 创建规则项HTML
   */
  createRuleItem(rule, isLocal) {
    return `
      <div class="rule-item ${rule.isActive ? 'active' : 'inactive'}" data-rule-id="${rule.id}">
        <div class="rule-header">
          <div class="rule-info">
            <div class="rule-platform">${this.escapeHtml(rule.platform)}</div>
            <div class="rule-sender">${this.escapeHtml(rule.senderEmail)}</div>
            <div class="rule-type">${rule.patternType === 'CODE' ? '验证码' : '链接'}</div>
          </div>
          <div class="rule-actions">
            ${isLocal ? `
              <button class="toggle-rule-btn" data-rule-id="${rule.id}" title="${rule.isActive ? '禁用' : '启用'}">
                ${rule.isActive ? '🟢' : '🔴'}
              </button>
              <button class="delete-rule-btn" data-rule-id="${rule.id}" title="删除">🗑️</button>
            ` : ''}
          </div>
        </div>
        <div class="rule-details">
          <div class="rule-pattern">模式: ${this.escapeHtml(rule.pattern)}</div>
          <div class="rule-time">创建: ${new Date(rule.createdTime).toLocaleString()}</div>
        </div>
      </div>
    `;
  }

  /**
   * 测试规则
   */
  async testRule() {
    const form = this.elements.ruleCreatorForm;
    const formData = new FormData(form);

    const rule = {
      platform: formData.get('platform'),
      senderEmail: formData.get('senderEmail'),
      pattern: formData.get('pattern'),
      patternType: formData.get('patternType')
    };

    const testContent = formData.get('example');

    if (!rule.pattern || !testContent) {
      this.showError('请填写规则模式和测试内容');
      return;
    }

    const result = emailParser.testRule(testContent, rule);

    this.elements.testResult.style.display = 'block';
    this.elements.testResultContent.innerHTML = `
      <div class="test-status ${result.success ? 'success' : 'error'}">
        ${result.message}
      </div>
      ${result.matches && result.matches.length > 0 ? `
        <div class="test-matches">
          <h5>匹配结果:</h5>
          <ul>
            ${result.matches.map(match => `<li>${this.escapeHtml(match)}</li>`).join('')}
          </ul>
        </div>
      ` : ''}
    `;
  }

  /**
   * 保存本地规则
   */
  async saveLocalRule() {
    const form = this.elements.ruleCreatorForm;
    const formData = new FormData(form);

    const ruleData = {
      platform: formData.get('platform'),
      senderEmail: formData.get('senderEmail'),
      pattern: formData.get('pattern'),
      patternType: formData.get('patternType'),
      example: formData.get('example')
    };

    try {
      const rule = ruleManager.createRule(ruleData);
      const success = await ruleManager.saveLocalRule(rule);

      if (success) {
        this.showSuccess('本地规则保存成功');
        form.reset();
        this.elements.testResult.style.display = 'none';
        await this.loadLocalRules();
      } else {
        this.showError('保存本地规则失败');
      }
    } catch (error) {
      console.error('保存本地规则失败:', error);
      this.showError('保存失败: ' + error.message);
    }
  }

  /**
   * 共享规则
   */
  async shareRule() {
    const form = this.elements.ruleCreatorForm;
    const formData = new FormData(form);

    const ruleData = {
      platform: formData.get('platform'),
      senderEmail: formData.get('senderEmail'),
      pattern: formData.get('pattern'),
      patternType: formData.get('patternType'),
      example: formData.get('example')
    };

    try {
      const rule = ruleManager.createRule(ruleData);
      const result = await ruleManager.uploadSharedRule(rule);

      if (result.success) {
        this.showSuccess('规则共享成功');
        form.reset();
        this.elements.testResult.style.display = 'none';
        await this.loadSharedRules();
      } else {
        this.showError('共享失败: ' + result.message);
      }
    } catch (error) {
      console.error('共享规则失败:', error);
      this.showError('共享失败: ' + error.message);
    }
  }
}

// 初始化应用
const app = new SidePanelApp();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  app.init();
});
