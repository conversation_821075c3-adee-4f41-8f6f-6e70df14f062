您好，我已经收到了您提供的关于临时邮箱服务的详细需求说明。这是一个非常完整的技术文档，我将它转化为一个清晰、结构化的插件开发需求说明书，重点突出插件需要实现的功能和与后端服务的交互逻辑。

---

## 临时邮箱服务浏览器插件需求说明

### 概述
该插件旨在为用户提供一个方便快捷的临时邮箱服务，用于接收第三方平台的验证码或注册链接。通过结合用户自有域名、126邮箱邮件转发功能以及后端云服务器，本插件将实现临时邮箱的生成、邮件接收与展示、以及验证码/链接的智能解析功能。

该插件将作为Edge浏览器扩展程序发布。

---

### 插件核心功能

#### 1. 临时邮箱生成与管理
* **生成临时邮箱：** 插件将根据用户配置的域名，在本地生成一个8到12位随机字符串的邮箱地址。例如：`<EMAIL>`。
* **记录生成时间：** 插件将自动记录临时邮箱的生成时间，作为后续向后端服务器请求邮件的筛选依据。
* **发送请求：** 插件将通过安全的API通道（HTTPS POST）向云服务器发送生成的临时邮箱地址和其生成时间，并附带一个唯一的用户ID（基于时间戳和随机数组合），用于后端数据隔离。

#### 2. 邮件接收与展示
* **实时获取：** 插件将持续通过API（HTTPS GET）向云服务器轮询，获取新接收到的、发送至该临时邮箱的邮件数据。
* **邮件列表显示：** 接收到邮件数据后，插件将在界面上以清晰的列表形式展示邮件。每封邮件都应包含以下信息，并提供展开/收回功能：
    * **发件人**
    * **邮件主题**
    * **提取的验证码**（附带一键复制按钮）
    * **提取的链接**（附带一键复制按钮）
    * **邮件内容**（可展开查看完整内容）
* **未解析内容处理：** 如果没有解析出验证码或链接，插件将完整展示邮件原文，而“提取的验证码”和“提取的链接”区域将留空。

#### 3. 智能解析规则管理
* **解析规则创建：** 插件将提供一个用户界面，允许用户创建自定义解析规则。用户需要输入目标平台的发件人邮箱、提供一封完整的邮件示例，并手动标记出需要提取的验证码或链接。
* **本地存储：** 用户可以选择将创建的解析规则保存在本地浏览器存储中，仅供当前用户使用。
* **共享规则：** 用户也可以选择将解析规则上传至云服务器，以便共享给所有插件用户。
* **规则应用逻辑：** 插件将优先使用本地保存的解析规则。如果本地没有匹配规则，则会向云服务器请求共享规则库，进行二次匹配和解析。解析过程完全在插件本地完成，以减少服务器资源消耗。

---

### 架构与技术要求

* **轻量化设计：** 插件端将承担大部分业务逻辑，特别是邮件内容的解析工作。云服务器仅负责邮件的接收和转发，以及共享解析规则的存储与分发。
* **API密钥：** 插件与云服务器的API连接密钥可以直接硬编码在插件代码中，以简化配置。
* **安全性：** 插件发送的用户ID和临时邮箱信息只用于本次请求，服务器不存储任何持久化的用户数据或邮件内容。
* **效率要求：** 插件需在5秒内获取到临时邮箱接收到的新邮件。
* **代码结构：** 插件所有代码需放置在独立的 `plugin` 文件夹中。
* **图标资源：** 插件图标文件位于 `plugin/icons/icon.png` 路径下。

---

### 数据流转与交互流程

1.  用户打开插件，点击“生成”按钮。
2.  **插件**在本地生成临时邮箱地址，记录生成时间，并发送（POST）至服务器API。
3.  **服务器**接收请求，立即启动POP3连接中转邮箱，**只收取生成时间之后**的新邮件。
4.  **服务器**过滤出发送至该临时邮箱的邮件，并将邮件数据（发件人、主题、内容等）通过API（GET）返回给插件。
5.  **插件**接收到邮件数据后，立即开始解析流程：
    * 首先使用**本地存储的解析规则**匹配。
    * 如果匹配失败，则向服务器请求**共享规则**，进行再次匹配。
    * 根据匹配结果，插件在界面上展示提取的验证码/链接或完整的邮件内容。
6.  用户创建解析规则并选择“共享”时，**插件**会将规则数据通过API（POST）发送至服务器进行持久化存储。
7.  插件每次需要解析时，都可以向服务器请求获取共享规则。

---

### 服务器端API接口需求

（以下为插件需要调用的服务器端API）

* **`POST /api/generate-email`**
    * **功能：** 发送临时邮箱地址和生成时间，触发后端邮件收取流程。
    * **参数：** 临时邮箱地址、生成时间、用户ID。
    * **响应：** 成功或失败状态。

* **`GET /api/get-emails`**
    * **功能：** 获取指定用户ID对应的最新邮件数据。
    * **参数：** 用户ID。
    * **响应：** 邮件数据列表。

* **`POST /api/upload-shared-rule`**
    * **功能：** 上传用户创建的共享解析规则。
    * **参数：** 规则数据（发件人邮箱、解析模式等）。
    * **响应：** 成功或失败状态。

* **`GET /api/get-shared-rules`**
    * **功能：** 获取所有共享的解析规则。
    * **参数：** 无。
    * **响应：** 共享解析规则列表。